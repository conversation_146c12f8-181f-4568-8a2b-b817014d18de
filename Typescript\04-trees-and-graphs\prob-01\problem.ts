/**
 * Problem: Maximum Depth of Binary Tree
 * 
 * Given the root of a binary tree, return its maximum depth.
 * 
 * A binary tree's maximum depth is the number of nodes along the longest path 
 * from the root node down to the farthest leaf node.
 * 
 * Example 1:
 * Input: root = [3,9,20,null,null,15,7]
 * Output: 3
 * 
 * Example 2:
 * Input: root = [1,null,2]
 * Output: 2
 * 
 * Constraints:
 * - The number of nodes in the tree is in the range [0, 104].
 * - -100 <= Node.val <= 100
 */

// Definition for a binary tree node
export class TreeNode {
    val: number;
    left: TreeNode | null;
    right: TreeNode | null;
    
    constructor(val: number = 0, left: TreeNode | null = null, right: TreeNode | null = null) {
        this.val = val;
        this.left = left;
        this.right = right;
    }
}

// Helper function to create a binary tree from an array (level-order traversal)
export function createBinaryTree(values: (number | null)[]): TreeNode | null {
    if (values.length === 0 || values[0] === null) return null;
    
    const root = new TreeNode(values[0]!);
    const queue: (TreeNode | null)[] = [root];
    let i = 1;
    
    while (queue.length > 0 && i < values.length) {
        const node = queue.shift();

        if (node !== null && node !== undefined) {
            // Left child
            if (i < values.length && values[i] !== null) {
                node.left = new TreeNode(values[i]!);
                queue.push(node.left);
            } else {
                queue.push(null);
            }
            i++;
            
            // Right child
            if (i < values.length && values[i] !== null) {
                node.right = new TreeNode(values[i]!);
                queue.push(node.right);
            } else {
                queue.push(null);
            }
            i++;
        }
    }
    
    return root;
}

// Recursive solution (DFS)
export function maxDepthRecursive(root: TreeNode | null): number {
    if (root === null) return 0;
    
    const leftDepth = maxDepthRecursive(root.left);
    const rightDepth = maxDepthRecursive(root.right);
    
    return Math.max(leftDepth, rightDepth) + 1;
}

// Iterative solution using BFS (level-order traversal)
export function maxDepthBFS(root: TreeNode | null): number {
    if (root === null) return 0;
    
    const queue: TreeNode[] = [root];
    let depth = 0;
    
    while (queue.length > 0) {
        const levelSize = queue.length;
        
        for (let i = 0; i < levelSize; i++) {
            const node = queue.shift()!;
            
            if (node.left) queue.push(node.left);
            if (node.right) queue.push(node.right);
        }
        
        depth++;
    }
    
    return depth;
}

// Iterative solution using DFS with stack
export function maxDepthDFS(root: TreeNode | null): number {
    if (root === null) return 0;
    
    const stack: { node: TreeNode; depth: number }[] = [{ node: root, depth: 1 }];
    let maxDepth = 0;
    
    while (stack.length > 0) {
        const { node, depth } = stack.pop()!;
        maxDepth = Math.max(maxDepth, depth);
        
        if (node.right) {
            stack.push({ node: node.right, depth: depth + 1 });
        }
        
        if (node.left) {
            stack.push({ node: node.left, depth: depth + 1 });
        }
    }
    
    return maxDepth;
} 